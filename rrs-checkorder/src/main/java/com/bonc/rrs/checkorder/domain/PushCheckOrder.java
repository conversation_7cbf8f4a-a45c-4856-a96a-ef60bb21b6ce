package com.bonc.rrs.checkorder.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检查订单信息
 * @Description: 5.1 CPIM检查订单信息推送服务商
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查订单信息")
public class PushCheckOrder {

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("任务序号")
    private String taskSerial;

    @ApiModelProperty("子订单编号")
    private String subOrderCode;

    @ApiModelProperty("订单类型")
    private String orderType;

    @ApiModelProperty("充电桩编码")
    private String wallboxCode;

    @ApiModelProperty("质保截止日期")
    private String warrantyEnd;

    @ApiModelProperty("派单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dispatchTime;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系人手机号")
    private String contactMobile;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省")
    private String province;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市")
    private String city;

    @ApiModelProperty("区编码")
    private String areaCode;

    @ApiModelProperty("区")
    private String area;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("充电桩名称")
    private String wallboxName;

    @ApiModelProperty("充电桩功率")
    private String wallboxPower;

    @ApiModelProperty("品牌")
    private String carBrand;

    @ApiModelProperty("安装订单号")
    private String installOrderCode;

    @ApiModelProperty("是否推送安装信息")
    private String pushInstallation;

    @ApiModelProperty("安装完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date installationCompletedTime;

    @ApiModelProperty("物料编码")
    private String wallboxMaterialCode;

    @ApiModelProperty("取电方式")
    private String powerSupplyMethod;

    @ApiModelProperty("线缆品牌")
    private String cableBrand;

    @ApiModelProperty("线缆规格")
    private String cableType;

    @ApiModelProperty("线缆长度")
    private String cableLength;

    @ApiModelProperty("断路器品牌")
    private String breakerBrand;

    @ApiModelProperty("断路器型号")
    private String breakerType;

    @ApiModelProperty("是否安装立柱")
    private String installStake;

    @ApiModelProperty("是否安装保护箱")
    private String installProtectingBox;

    @ApiModelProperty("是否接地极")
    private String groundElectrode;

    @ApiModelProperty("前端线材")
    private String frontEndCable;

    @ApiModelProperty("图片附件")
    private String picAttrs;
}
