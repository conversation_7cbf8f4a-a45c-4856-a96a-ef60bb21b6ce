package com.bonc.rrs.checkorder.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 接口返回值实体类
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OtherApiResponse implements Serializable {

    private int errno;
    private String errmsg;

    private Object data;

    public static OtherApiResponse ok() {
        return new OtherApiResponse(0, "成功", null);
    }

    public static OtherApiResponse error(int errno, String errmsg) {
        return new OtherApiResponse(errno, errmsg, null);
    }
}
