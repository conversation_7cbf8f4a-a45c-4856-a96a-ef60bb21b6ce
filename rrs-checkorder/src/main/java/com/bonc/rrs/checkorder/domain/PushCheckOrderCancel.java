package com.bonc.rrs.checkorder.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检查订单取消信息推送
 * @Description: 5.6 CPIM用户取消检查订单后推送取消信息至服务商
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查订单取消信息推送")
public class PushCheckOrderCancel {

    @ApiModelProperty("子订单订单编号")
    private String subOrderCode;

    @ApiModelProperty("订单取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelDate;
}
