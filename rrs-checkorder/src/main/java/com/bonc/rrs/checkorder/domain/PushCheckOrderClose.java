package com.bonc.rrs.checkorder.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检查订单关闭信息推送
 * @Description: 5.8 CPIM 关闭订单后推送关闭订单信息
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查订单关闭信息推送")
public class PushCheckOrderClose {

    @ApiModelProperty("子订单编号")
    private String subOrderCode;

    @ApiModelProperty("关闭原因")
    private String remark;

    @ApiModelProperty("关闭订单操作人")
    private String operatePerson;

    @ApiModelProperty("关闭订单操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;
}
