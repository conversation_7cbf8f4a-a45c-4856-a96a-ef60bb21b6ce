package com.bonc.rrs.checkorder.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 检查信息回传
 * @Description: 5.3 服务商回传检查信息
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查信息回传")
public class PushCheckProcessing implements Serializable {

    @ApiModelProperty("子订单编号")
    private String subOrderCode;

    @ApiModelProperty("操作人")
    private String operatePerson;

    @ApiModelProperty("服务完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date serviceFinishTime;

    @ApiModelProperty("附件")
    private String picAttrs;
}
