package com.bonc.rrs.checkorder.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * 接口返回值实体类
 * 
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PushApiResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 响应描述 */
    private String message;

    /** 响应内容 */
    private List<Object> data;

    public PushApiResponse(String message) {
        this.message = message;
    }
}
