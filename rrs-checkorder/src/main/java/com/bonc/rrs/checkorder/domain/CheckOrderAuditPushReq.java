package com.bonc.rrs.checkorder.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检查订单审核信息推送
 * @Description: 5.5 CPIM检查订单审核信息推送服务商
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查订单审核信息推送")
public class CheckOrderAuditPushReq {

    @ApiModelProperty("子订单编号")
    private String subOrderCode;

    @ApiModelProperty("审核结果")
    private String result;

    @ApiModelProperty("拒绝原因")
    private String remark;

    @ApiModelProperty("审核人")
    private String checkPerson;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkDate;
}
