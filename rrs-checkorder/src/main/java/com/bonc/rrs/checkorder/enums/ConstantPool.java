package com.bonc.rrs.checkorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * @Description: 检查工单常量或者枚举
 * @Author: liujunpeng
 * @Date: 2024/2/27 18:02
 * @Version: 1.0
 */
public class ConstantPool {

    /**
     * 比亚迪系统操作人ID
     */
    public static final Long BYD_OPERATOR = 89L;

    /**
     * 比亚迪系统操作人名称
     */
    public static final String BYD_OPERATOR_NAME = "到每家科技";

    /**
     * 比亚迪系统操作人名称
     */
    public static final String XK_OPERATOR_NAME = "小咖";

    /**
     * news系统默认操作人ID
     */
    public static final Long NEWS_OPERATOR = 89L;

    /**
     * news系统默认操作人名称
     */
    public static final String NEWS_OPERATOR_NAME = "系统自动";

    public static final String SUCCESS = "success";

    @Getter
    @AllArgsConstructor
    public enum BringWallbox {

        NULL("", ""),
        Y("1", "是"),
        N("0", "否");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(BringWallbox.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }
    }

    /**
     * 是否需要立柱
     */
    @Getter
    @AllArgsConstructor
    public enum NeedStake {
        NULL("", ""),
        Y("1", "是"),
        N("0", "否");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(NeedStake.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(NeedStake.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 车位状况
     */
    @Getter
    @AllArgsConstructor
    public enum CarportStatus {
        NULL("", ""),
        C1("1", "产权固定"),
        C2("2", "长租固定"),
        C3("3", "办公地车位"),
        C4("4", "自家工厂"),
        C5("5", "无固定车位"),
        C10("10", "其它");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(CarportStatus.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(CarportStatus.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 住宅类型
     */
    @Getter
    @AllArgsConstructor
    public enum HousingType {
        NULL("", ""),
        H1("1", "农村自建房"),
        H2("2", "城市小区"),
        H3("3", "城市自建房"),
        H4("4", "工厂"),
       // H5("5", "其它"), 废弃
        H100("100", "其它");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(HousingType.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(HousingType.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 是否需要电力报装
     */
    @Getter
    @AllArgsConstructor
    public enum EmeterRequestProgress {
        NULL("", ""),
        Y("1", "是"),
        N("0", "否");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(EmeterRequestProgress.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(EmeterRequestProgress.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 勘测结论
     */
    @Getter
    @AllArgsConstructor
    public enum SurveyResult {
        NULL("", ""),
        S1("1", "可以安装"),
        S2("2", "不可以安装");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(SurveyResult.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(SurveyResult.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 客户自提桩
     */
    @Getter
    @AllArgsConstructor
    public enum SelfPick {
        NULL("", ""),
        Y("1", "是"),
        N("0", "否");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(SelfPick.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(SelfPick.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 订单类型
     */
    @Getter
    @AllArgsConstructor
    public enum CarOwnerType {
        NULL("", ""),
        C10("10", "普通订单"),
        C20("20", "大定订单"),
        C30("30", "商城订单");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(CarOwnerType.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(CarOwnerType.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 财务归属
     */
    @Getter
    @AllArgsConstructor
    public enum FinancialAttribution {
        NULL("", ""),
        C10("10", "权益订单"),
        C20("20", "权益PLUS订单"),
        C30("30", "自费订单"),
        C40("40", "对公订单");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(FinancialAttribution.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(FinancialAttribution.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 取电方式
     */
    @Getter
    @AllArgsConstructor
    public enum PowerSupplyMethod {
        NULL("", ""),
        S1("1", "国网电"),
        S2("2", "物业电"),
        S3("3", "入户电"),
        S10("10", "其它");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(PowerSupplyMethod.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(PowerSupplyMethod.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }

    /**
     * 线缆品牌
     */
    @Getter
    @AllArgsConstructor
    public enum CableBrand {
        NULL("", ""),
        S1("1", "桂林国际"),
        S2("2", "恒飞线缆"),
        S3("3", "自布线"),
        S4("4", "联嘉祥"),
        //测试用的错误品牌比亚迪不存在5
        S5("5", "万马"),
        S6("6", "错误品牌"),
        S10("10", "其它");

        private String code;
        private String name;

        public String getName () {
            return this.name;
        }
        public static String getNameByCode(String code) {
            return Stream.of(CableBrand.values()).filter(c -> Objects.equals(c.getCode(), code)).findFirst().orElse(NULL).getName();
        }

        public static String getCodeByName(String name) {
            return Stream.of(CableBrand.values()).filter(c -> Objects.equals(c.getName(), name)).findFirst().orElse(NULL).getCode();
        }
    }
}
