package com.bonc.rrs.checkorder.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.checkorder.domain.*;
import com.bonc.rrs.checkorder.response.PushApiResponse;
import com.bonc.rrs.checkorder.service.CheckOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 检查工单控制器
 * @Description: 比亚迪检查工单相关接口
 * @Author: louis
 * @Date: 2025/07/24
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/checkorder")
@RequiredArgsConstructor
@Api(tags = "检查工单管理")
public class CheckOrderController {

    private final CheckOrderService checkOrderService;

    @PostMapping("/pushCheckOrderAudit")
    @ApiOperation(value = "CPIM 检查订单审核信息推送服务商接口", notes = "CPIM 检查订单审核信息推送服务商数据")
    public PushApiResponse checkOrderAuditInfoPush(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!checkOrderService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        CheckOrderAuditPushReq checkOrderAuditPushReq = JSON.parseObject(req.toJSONString(), CheckOrderAuditPushReq.class);
        // 参数校验
        String msg = checkOrderService.validateRequiredFields(checkOrderAuditPushReq);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return checkOrderService.checkOrderAuditInfoPush(checkOrderAuditPushReq);
    }

    @PostMapping("/pushCheckOrderCancel")
    @ApiOperation(value = "CPIM 用户取消检查订单后推送取消信息至服务商", notes = "CPIM 用户取消检查订单后推送取消信息至服务商")
    public PushApiResponse pushCheckOrderCancel(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!checkOrderService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderCancel pushCheckOrderCancel = JSON.parseObject(req.toJSONString(), PushCheckOrderCancel.class);
        // 参数校验
        String msg = checkOrderService.validateRequiredFields(pushCheckOrderCancel);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return checkOrderService.checkOrderCancel(pushCheckOrderCancel);
    }

    @PostMapping("/pushCheckOrderClose")
    @ApiOperation(value = "CPIM 关闭检查订单后推送关闭订单信息", notes = "CPIM 关闭检查订单后推送关闭订单信息")
    public PushApiResponse pushCheckOrderClose(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!checkOrderService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderClose pushCheckOrderClose = JSON.parseObject(req.toJSONString(), PushCheckOrderClose.class);
        // 参数校验
        String msg = checkOrderService.validateRequiredFields(pushCheckOrderClose);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return checkOrderService.checkOrderClose(pushCheckOrderClose);
    }

    @PostMapping("/pushCheckOrderSuspend")
    @ApiOperation(value = "CPIM 检查订单暂停信息推送服务商", notes = "CPIM 检查订单暂停信息推送服务商")
    public PushApiResponse pushCheckOrderSuspend(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!checkOrderService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderSuspend pushCheckOrderSuspend = JSON.parseObject(req.toJSONString(), PushCheckOrderSuspend.class);
        // 参数校验
        String msg = checkOrderService.validateRequiredFields(pushCheckOrderSuspend);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return checkOrderService.checkOrderSuspend(pushCheckOrderSuspend);
    }

    @PostMapping("/pushCheckOrderRestore")
    @ApiOperation(value = "CPIM 检查订单恢复执行信息推送服务商", notes = "CPIM 检查订单恢复执行信息推送服务商")
    public PushApiResponse pushCheckOrderRestore(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!checkOrderService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderRestore pushCheckOrderRestore = JSON.parseObject(req.toJSONString(), PushCheckOrderRestore.class);
        // 参数校验
        String msg = checkOrderService.validateRequiredFields(pushCheckOrderRestore);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return checkOrderService.checkOrderRestore(pushCheckOrderRestore);
    }
}
