package com.bonc.rrs.checkorder.service;

import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.checkorder.domain.*;
import com.bonc.rrs.checkorder.response.PushApiResponse;

import javax.servlet.http.HttpServletRequest;

/**
 * 检查工单服务接口
 * @Description: 检查工单业务处理服务
 * @Author: louis
 * @Date: 2025/07/24
 * @Version: 1.0
 */
public interface CheckOrderService {

    /**
     * 验证签名
     * @param request HTTP请求
     * @param req 请求参数
     * @return 验证结果
     */
    boolean verificationOfSignatures(HttpServletRequest request, JSONObject req);

    /**
     * 验证必填字段
     * @param obj 对象
     * @return 验证结果消息
     */
    String validateRequiredFields(Object obj);

    /**
     * 检查订单审核信息推送
     * @param req 审核信息
     * @return 响应结果
     */
    PushApiResponse checkOrderAuditInfoPush(CheckOrderAuditPushReq req);

    /**
     * 检查订单取消
     * @param req 取消信息
     * @return 响应结果
     */
    PushApiResponse checkOrderCancel(PushCheckOrderCancel req);

    /**
     * 检查订单关闭
     * @param req 关闭信息
     * @return 响应结果
     */
    PushApiResponse checkOrderClose(PushCheckOrderClose req);

    /**
     * 检查订单暂停
     * @param req 暂停信息
     * @return 响应结果
     */
    PushApiResponse checkOrderSuspend(PushCheckOrderSuspend req);

    /**
     * 检查订单恢复
     * @param req 恢复信息
     * @return 响应结果
     */
    PushApiResponse checkOrderRestore(PushCheckOrderRestore req);
}
