package com.bonc.rrs.checkorder.service;

import com.bonc.rrs.checkorder.domain.PushCheckProcessing;
import com.bonc.rrs.checkorder.domain.PushCheckReviewInfo;
import com.bonc.rrs.checkorder.response.OtherApiResponse;

/**
 * 检查工单API服务接口
 * @Description: 检查工单相关的API调用服务
 * @Author: louis
 * @Date: 2025/07/24
 * @Version: 1.0
 */
public interface ICheckOrderApiService {

    /**
     * 服务商回传检查信息
     * @param pushCheckProcessing 检查信息
     * @return 响应结果
     */
    OtherApiResponse pushCheckProcessing(PushCheckProcessing pushCheckProcessing);

    /**
     * 服务商检查订单提交审核
     * @param pushCheckReviewInfo 审核信息
     * @return 响应结果
     */
    OtherApiResponse pushCheckReviewInfo(PushCheckReviewInfo pushCheckReviewInfo);
}
