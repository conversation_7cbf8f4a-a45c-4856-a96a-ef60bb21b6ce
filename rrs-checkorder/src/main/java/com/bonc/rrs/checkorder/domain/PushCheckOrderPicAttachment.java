package com.bonc.rrs.checkorder.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 检查工单图片附件信息
 *
 * @Description: 检查工单相关的图片附件实体类
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel("检查工单图片附件信息")
public class PushCheckOrderPicAttachment implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "施工使用线缆")
    private String constructionImage;

    @ApiModelProperty(value = "充电桩序列码")
    private String sequenceImage;

    @ApiModelProperty(value = "用线始端")
    private String lineStartImage;

    @ApiModelProperty(value = "用线末端")
    private String lineEndImage;

    @ApiModelProperty(value = "接地线或接地极")
    private String groundWireImage;

    @ApiModelProperty(value = "电源点火零电压")
    private String zeroVoltageImage;

    @ApiModelProperty(value = "人桩合照")
    private String manPileImage;

    @ApiModelProperty(value = "安装确认单")
    private String confirmationImage;

    @ApiModelProperty(value = "增项收费单")
    private String increaseChargeImage;

    @ApiModelProperty(value = "漏保上端火零绝缘电阻")
    private String fireZeroResistanceImage;

    @ApiModelProperty(value = "漏保下端零地电压")
    private String zeroGroundVoltageImage;

    @ApiModelProperty(value = "试充照片")
    private String trialChargeImage;

    @ApiModelProperty(value = "充电桩铭牌图片")
    private String pileNameplateImage;

    @ApiModelProperty(value = "放弃电力报装免责声明")
    private String disclaimersImage;

    @ApiModelProperty(value = "同级负载确认书")
    private String loadConfirmationImage;
}
