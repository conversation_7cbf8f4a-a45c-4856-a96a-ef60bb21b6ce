package com.bonc.rrs.checkorder.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检查订单恢复执行信息推送
 * @Description: 5.10 CPIM检查订单恢复执行信息推送服务商
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查订单恢复执行信息推送")
public class PushCheckOrderRestore {

    @ApiModelProperty("子订单编号")
    private String subOrderCode;

    @ApiModelProperty("操作人")
    private String operatePerson;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;
}
