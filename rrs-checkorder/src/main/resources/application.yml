server:
  port: 8081
  servlet:
    context-path: /checkorder

spring:
  application:
    name: rrs-checkorder
  profiles:
    active: dev

# 日志配置
logging:
  level:
    com.bonc.rrs.checkorder: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Swagger配置
swagger:
  enabled: true
  title: 检查工单系统API
  description: 比亚迪检查工单系统接口文档
  version: 1.0.0
  base-package: com.bonc.rrs.checkorder.controller
