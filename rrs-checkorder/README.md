# RRS-CheckOrder 检查工单系统

## 项目简介

`rrs-checkorder` 是从 `rrs-worder` 模块中拆分出来的比亚迪检查工单功能模块，专门负责处理比亚迪检查工单相关的业务逻辑。

## 功能特性

### 核心功能
- 检查工单审核信息推送处理
- 检查工单取消信息处理
- 检查工单关闭信息处理
- 检查工单暂停信息处理
- 检查工单恢复执行信息处理

### 技术特性
- 基于 Spring Boot 2.1.3
- 使用 MyBatis Plus 进行数据访问
- 集成 Swagger 接口文档
- 支持分布式锁
- 支持消息队列处理

## 模块结构

```
rrs-checkorder/
├── src/main/java/com/bonc/rrs/checkorder/
│   ├── controller/          # 控制器层
│   ├── service/            # 服务层
│   │   └── impl/          # 服务实现
│   ├── dao/               # 数据访问层
│   ├── entity/            # 实体类
│   ├── domain/            # 领域对象
│   ├── enums/             # 枚举类
│   ├── response/          # 响应对象
│   ├── util/              # 工具类
│   └── common/            # 公共组件
├── src/main/resources/
│   ├── mapper/checkorder/ # MyBatis映射文件
│   ├── static/            # 静态资源
│   ├── templates/         # 模板文件
│   └── application.yml    # 配置文件
└── src/test/java/         # 测试代码
```

## 主要接口

### 检查工单管理接口

1. **检查订单审核信息推送**
   - 接口：`POST /checkorder/pushCheckOrderAudit`
   - 功能：处理CPIM检查订单审核信息推送

2. **检查订单取消信息推送**
   - 接口：`POST /checkorder/pushCheckOrderCancel`
   - 功能：处理用户取消检查订单后的信息推送

3. **检查订单关闭信息推送**
   - 接口：`POST /checkorder/pushCheckOrderClose`
   - 功能：处理关闭检查订单后的信息推送

4. **检查订单暂停信息推送**
   - 接口：`POST /checkorder/pushCheckOrderSuspend`
   - 功能：处理检查订单暂停信息推送

5. **检查订单恢复执行信息推送**
   - 接口：`POST /checkorder/pushCheckOrderRestore`
   - 功能：处理检查订单恢复执行信息推送

## 依赖关系

### 核心依赖
- `lenmon-core`: 核心工具类和公共组件
- `lenmon-sys`: 系统管理功能
- `lenmon-activiti`: 工作流引擎

### 第三方依赖
- Spring Boot Starter Web
- Spring Boot Starter Cache
- Spring Boot Starter AMQP
- MyBatis Plus
- Swagger2
- Lombok
- FastJSON

## 配置说明

### 应用配置
- 默认端口：8081
- 上下文路径：/checkorder
- 默认环境：dev

### 日志配置
- 模块日志级别：DEBUG
- 根日志级别：INFO

## 开发指南

### 环境要求
- JDK 1.8+
- Maven 3.6+
- Spring Boot 2.1.3

### 构建运行
```bash
# 编译
mvn clean compile

# 打包
mvn clean package

# 运行
java -jar target/rrs-checkorder-1.0-SNAPSHOT.jar
```

### 接口文档
启动应用后访问：http://localhost:8081/checkorder/swagger-ui.html

## 迁移说明

本模块是从 `rrs-worder` 模块中拆分出来的，主要迁移了以下内容：

### 已迁移的组件
- 检查工单相关的实体类和DTO
- 检查工单相关的控制器接口
- 检查工单相关的服务层逻辑
- 检查工单相关的响应对象
- 检查工单相关的枚举和常量

### 待完善的功能
- 数据访问层的完整实现
- 定时任务的迁移和适配
- 业务逻辑的完整实现
- 与其他模块的集成

## 注意事项

1. 本模块目前处于初始拆分阶段，部分功能可能需要进一步完善
2. 签名验证和字段验证逻辑需要根据实际业务需求实现
3. 数据库相关配置需要根据实际环境进行配置
4. 与原 `rrs-worder` 模块的依赖关系需要逐步解耦

## 版本历史

- v1.0.0: 初始版本，从 rrs-worder 模块拆分检查工单功能
