package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushCheckCancelReviewInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 服务商检查订单取消审核信息回传
 * @Description /jumpto/openapi/sp/et/pushCancelReviewInfo
 * @Date 2025/07/21
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushCheckCancelReviewInfo extends AbstractBusinessProcess {

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushCheckCancelReviewInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        try {
            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }

            PushCheckCancelReviewInfo pushCheckCancelReviewInfo = new PushCheckCancelReviewInfo();
            pushCheckCancelReviewInfo.setSubOrderCode(worderInformationEntity.getCompanyOrderNumber());
            pushCheckCancelReviewInfo.setResult("2"); // 取消审核
            pushCheckCancelReviewInfo.setCheckPerson(StringUtils.defaultIfBlank(businessProcessPo.getOperator(), OPERATE_PERSON));
            
            // 服务商检查订单取消审核信息回传
            OtherApiResponse otherApiResponse = iBydApiService.pushCheckCancelReviewInfo(pushCheckCancelReviewInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        } catch (Exception e) {
            log.error("检查工单取消审核信息回传出现异常", e);
            return Result.error("检查工单取消审核信息回传失败");
        }
        return Result.success();
    }
} 