package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushMovePileInstallSocket;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 服务商回传客户是否存在挪桩，加装插座意向
 * @Description /jumpto/openapi/sp/pushMovePileInstallSocket
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushMovePileInstallSocket extends AbstractBusinessProcess {

    final WorderExtFieldService worderExtFieldService;

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushMovePileInstallSocket";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        try {
            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }
            //TODO 工单扩展字段 客户是否存在挪桩、加装插座意向
            List<WorderExtFieldEntity> extFieldEntities = worderExtFieldService.getSpecificFields(businessProcessPo.getWorderNo(), Collections.singleton(12345));
            Map<Integer, String> fieldMap = new HashMap<>();
            for (WorderExtFieldEntity extFieldEntity : extFieldEntities) {
                fieldMap.put(extFieldEntity.getFieldId(), extFieldEntity.getFieldValue());
            }

            //故障是否解决
            String valueByName = YesOrNo.getValueByName(fieldMap.get(12345));
            if (valueByName == null) {
                return Result.error("客户是否存在挪桩、加装插座意向不能为空");
            }

            PushMovePileInstallSocket pushMovePileInstallSocket = PushMovePileInstallSocket.builder()
                    .orderCode(worderInformationEntity.getCompanyOrderNumber())
                    .movePileInstallSocket(valueByName)
                    .operatePerson(OPERATE_PERSON)
                    .build();

            OtherApiResponse otherApiResponse = iBydApiService.pushMovePileInstallSocket(pushMovePileInstallSocket);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }

        } catch (Exception e) {
            log.error("服务商回传客户是否存在挪桩，加装插座意向回传出现异常", e);
            return Result.error("服务商回传客户是否存在挪桩，加装插座意向回传失败");
        }
        return Result.success();
    }

    //创建枚举类：是--1，否--0
    @Getter
    private enum YesOrNo {
        YES("是", "1"),
        NO("否", "0");
        private final String name;
        private final String value;
        YesOrNo(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public static String getValueByName(String name) {
            for (YesOrNo yesOrNo : YesOrNo.values()) {
                if (yesOrNo.getName().equals(name)) {
                    return yesOrNo.getValue();
                }
            }
            return null;
        }
    }

}
