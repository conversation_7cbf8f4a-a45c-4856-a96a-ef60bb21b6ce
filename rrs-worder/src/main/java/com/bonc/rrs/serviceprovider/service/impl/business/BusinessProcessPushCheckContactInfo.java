package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushCheckContactInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.youngking.lenmoncore.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 服务商业务-检查工单联系信息回传
 * @Description /jumpto/openapi/sp/et/pushContactInfo
 * @Date 2025/07/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessProcessPushCheckContactInfo extends AbstractBusinessProcess {

    @Autowired
    private WorderRemarkLogService worderRemarkLogService;

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushCheckContactInfo";
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
        if (worderInformationEntity == null) {
            return Result.error("非法工单号");
        }
        try {
            List<WorderRemarkLogEntity> worderRemarkLogEntityList = worderRemarkLogService.queryConnectTime(worderInformationEntity.getWorderNo());

            PushCheckContactInfo pushCheckContactInfo = new PushCheckContactInfo();
            pushCheckContactInfo.setSubOrderCode(worderInformationEntity.getCompanyOrderNumber());
            String conveyAppointTime = worderInformationEntity.getConveyAppointTime();
            String installAppointTime = worderInformationEntity.getInstallAppointTime();

            pushCheckContactInfo.setOperatePerson(OPERATE_PERSON);
            if (worderRemarkLogEntityList != null && worderRemarkLogEntityList.size() > 0) {
                pushCheckContactInfo.setFirstContactTime(worderRemarkLogEntityList.get(0).getCreateTime());
            } else {
                pushCheckContactInfo.setFirstContactTime(new Date());
            }
            
            // 设置预计上门服务时间（使用安装预约时间或勘测预约时间）
            if (StringUtils.isNotBlank(businessProcessPo.getConveyAppointTime())) {
                conveyAppointTime = businessProcessPo.getConveyAppointTime();
            } else if (StringUtils.isNotBlank(businessProcessPo.getInstallAppointTime())) {
                installAppointTime = businessProcessPo.getInstallAppointTime();
            }

            // 优先使用安装预约时间，如果没有则使用勘测预约时间
            String planToServeTime = getDateField(installAppointTime);
            if (StringUtils.isBlank(planToServeTime)) {
                planToServeTime = getDateField(conveyAppointTime);
            }
            
            if (StringUtils.isNotBlank(planToServeTime)) {
                pushCheckContactInfo.setPlanToServeTime(DateUtils.stringToDate(planToServeTime, DateUtils.DATE_TIME_PATTERN));
            }

            // 调用通知接口
            OtherApiResponse otherApiResponse = iBydApiService.pushCheckContactInfo(pushCheckContactInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        } catch (Exception e) {
            log.error("检查工单联系信息回传出现异常", e);
            return Result.error("检查工单联系信息回传失败");
        }
        return Result.success();
    }

    private String getDateField(String date) {
        if (StringUtils.isNotEmpty(date) && date.length() >= 12) {
            if (date.length() == 19) {
                return date;
            } else if (date.length() > 19) {
                return date.substring(0, 19);
            } else if (date.length() > 12 && date.length() < 19) {
                date = date.substring(0, 13);
            }
            return DateUtils.format(DateUtils.stringToDate(date, "yyyy-MM-dd HH"), "yyyy-MM-dd HH:59:59");
        } else if (StringUtils.isNotEmpty(date) && date.length() >= 10 && date.length() < 12) {
            if (date.length() > 10) {
                date = date.substring(0, 10);
            }
            return DateUtils.format(DateUtils.stringToDate(date, "yyyy-MM-dd"), "yyyy-MM-dd 23:59:59");
        }
        return null;
    }
} 