package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushCheckReviewInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 服务商检查订单提交审核回传
 * @Description /jumpto/openapi/sp/et/pushReviewInfo
 * @Date 2025/07/21
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushCheckReviewInfo extends AbstractBusinessProcess {

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushCheckReviewInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        try {
            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }

            PushCheckReviewInfo pushCheckReviewInfo = new PushCheckReviewInfo();
            pushCheckReviewInfo.setOperatePerson(StringUtils.defaultIfBlank(businessProcessPo.getOperator(), OPERATE_PERSON));
            pushCheckReviewInfo.setSubOrderCode(worderInformationEntity.getCompanyOrderNumber());
            pushCheckReviewInfo.setCommitDate(new Date());
            
            // 服务商检查订单提交审核
            OtherApiResponse otherApiResponse = iBydApiService.pushCheckReviewInfo(pushCheckReviewInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        } catch (Exception e) {
            log.error("检查工单提交审核回传出现异常", e);
            return Result.error("检查工单提交审核回传失败");
        }
        return Result.success();
    }
} 