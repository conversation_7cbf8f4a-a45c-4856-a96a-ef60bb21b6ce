package com.bonc.rrs.serviceprovider.service.impl.business;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.byd.domain.PushCheckProcessing;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 服务商检查信息回传
 * @Description /jumpto/openapi/sp/et/pushProcessing
 * @Date 2025/07/21
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushCheckProcessing extends AbstractBusinessProcess {

    final WorderExtFieldService worderExtFieldService;

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushCheckProcessing";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        try {
            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }
            // 查询工单扩展字段
            List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldService.list(new QueryWrapper<>(WorderExtFieldEntity.builder().worderNo(businessProcessPo.getWorderNo()).build()));
            // 转Map
            Map<Integer, String> filedMap = new HashMap<>(32);
            worderExtFieldEntities.forEach(item -> filedMap.put(item.getFieldId(), item.getFieldValue()));

            PushCheckProcessing pushCheckProcessing = new PushCheckProcessing();
            pushCheckProcessing.setSubOrderCode(worderInformationEntity.getCompanyOrderNumber());
            pushCheckProcessing.setOperatePerson(OPERATE_PERSON);
            
            // 设置服务完成时间（使用检查完成时间字段）
            String serviceFinishTime = filedMap.get(3034); // 假设检查完成时间字段ID为3034
            if (StringUtils.isNotBlank(serviceFinishTime)) {
                pushCheckProcessing.setServiceFinishTime(java.sql.Date.valueOf(serviceFinishTime));
            }
            
            // 设置图片附件（将扩展字段中的图片信息组装成JSON）
            String picAttrs = buildCheckPicAttrs(filedMap);
            pushCheckProcessing.setPicAttrs(picAttrs);

            // 调用通知接口
            OtherApiResponse otherApiResponse = iBydApiService.pushCheckProcessing(pushCheckProcessing);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        } catch (Exception e) {
            log.error("检查工单检查信息回传出现异常", e);
            return Result.error("检查工单检查信息回传失败");
        }
        return Result.success();
    }

    /**
     * 构建检查工单图片附件JSON
     * @param filedMap 扩展字段Map
     * @return 图片附件JSON字符串
     */
    private String buildCheckPicAttrs(Map<Integer, String> filedMap) {
        // 创建图片附件对象
        PushCheckProcessing.PicAttachment picAttachment = new PushCheckProcessing.PicAttachment();
        
        // 设置检查结果确认单
        picAttachment.setExamineResultImage(filedMap.get(3026)); // 安装确认单字段
        
        // 设置人桩合照
        picAttachment.setManPileImage(filedMap.get(3025)); // 人桩合照字段
        
        // 设置取电点照片
        picAttachment.setGetPowerPointImage(filedMap.get(3035)); // 假设取电点照片字段ID为3035
        
        // 设置取电点火零照片
        picAttachment.setGetPowerPointFireZeroImage(filedMap.get(3036)); // 假设取电点火零照片字段ID为3036
        
        // 设置火地照片
        picAttachment.setFireEarthImage(filedMap.get(3037)); // 假设火地照片字段ID为3037
        
        // 设置零地电压照片
        picAttachment.setZeroEarthVoltageImage(filedMap.get(3029)); // 漏保下端零地电压字段
        
        // 设置取电点断路器至配电盒布线路由照片
        picAttachment.setGetPowerPointBreakerToPDBImage(filedMap.get(3038)); // 假设字段ID为3038
        
        // 设置配电盒照片
        picAttachment.setPdbImage(filedMap.get(3039)); // 假设配电盒照片字段ID为3039
        
        // 设置漏保照片
        picAttachment.setLeakageProtectImage(filedMap.get(3040)); // 假设漏保照片字段ID为3040
        
        // 设置漏保上端火地绝缘电阻照片
        picAttachment.setLeakageProtectUpResistanceImage(filedMap.get(3041)); // 假设字段ID为3041
        
        // 设置火零绝缘电阻照片
        picAttachment.setFireZeroResistanceImage(filedMap.get(3028)); // 漏保上端火零绝缘电阻字段
        
        // 设置接地电阻照片
        picAttachment.setEarthResistanceImage(filedMap.get(3042)); // 假设接地电阻照片字段ID为3042
        
        // 设置配电盒至充电桩布线路由照片
        picAttachment.setPdbtoPileImage(filedMap.get(3043)); // 假设字段ID为3043
        
        // 设置试充照片
        picAttachment.setTrialChargeImage(filedMap.get(3030)); // 试充照片字段
        
        // 设置枪头照片
        picAttachment.setGunHeadImage(filedMap.get(3044)); // 假设枪头照片字段ID为3044
        
        // 设置枪线照片
        picAttachment.setGunLineImage(filedMap.get(3045)); // 假设枪线照片字段ID为3045
        
        // 转换为JSON字符串
        return com.alibaba.fastjson.JSON.toJSONString(picAttachment);
    }
} 