package com.bonc.rrs.byd.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 检查信息回传
 * @Description: 5.3 服务商回传检查信息
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查信息回传")
public class PushCheckProcessing implements Serializable {

    @ApiModelProperty("子订单编号")
    private String subOrderCode;

    @ApiModelProperty("操作人")
    private String operatePerson;

    @ApiModelProperty("服务完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date serviceFinishTime;

    @ApiModelProperty("附件")
    private String picAttrs;

    @Data
    public static class PicAttachment {
        @NotBlank(message = "检查结果确认单不能为空")
        private String examineResultImage;

        @NotBlank(message = "人桩合照不能为空")
        private String manPileImage;

        @NotBlank(message = "取电点照片不能为空")
        private String getPowerPointImage;

        @NotBlank(message = "取电点火零照片不能为空")
        private String getPowerPointFireZeroImage;

        @NotBlank(message = "火地照片不能为空")
        private String fireEarthImage;

        @NotBlank(message = "零地电压照片不能为空")
        private String zeroEarthVoltageImage;

        @NotBlank(message = "取电点断路器至配电盒布线路由照片不能为空")
        private String getPowerPointBreakerToPDBImage;

        @NotBlank(message = "配电盒照片不能为空")
        private String pdbImage;

        @NotBlank(message = "漏保照片不能为空")
        private String leakageProtectImage;

        @NotBlank(message = "漏保上端火地绝缘电阻照片不能为空")
        private String leakageProtectUpResistanceImage;

        @NotBlank(message = "火零绝缘电阻照片不能为空")
        private String fireZeroResistanceImage;

        @NotBlank(message = "接地电阻照片不能为空")
        private String earthResistanceImage;

        @NotBlank(message = "配电盒至充电桩布线路由照片不能为空")
        private String pdbtoPileImage;


        @NotBlank(message = "试充照片不能为空")
        private String trialChargeImage;

        @NotBlank(message = "枪头照片不能为空")
        private String gunHeadImage;

        @NotBlank(message = "枪线照片不能为空")
        private String gunLineImage;
    }
}
