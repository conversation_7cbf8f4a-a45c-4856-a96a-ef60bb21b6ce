package bonc.storage.modules.storemanager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 网点主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:58
 */
@Data
@TableName("dot_information")
@ApiModel(value = "网点主表 ")
public class DotInformationEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    @ApiModelProperty(value = "主键ID", required = false)
    private Integer dotId;
    /**
     * 网点编号
     */
    @ApiModelProperty(value = "网点编号", required = false)
    @TableField(exist = false)
    private String dotNo;
    /**
     * 网点名称
     */
    @ApiModelProperty(value = "网点名称", required = false)
    private String dotName;

    /**
     * 网点状态
     */
    @ApiModelProperty(value = "网点状态", required = false)
    private Integer dotState;

    /**
     * 网点区域
     */
    @ApiModelProperty(value = "网点区域", required = false)
    private String dotArea;
    /**
     * 网点地址
     */
    @ApiModelProperty(value = "网点地址", required = false)
    private String dotAddress;
    /**
     * 网点营业执照
     */
    @ApiModelProperty(value = "网点营业执照", required = false)
    private String dotCertificate;
    /**
     * 记录创建时间
     */
    @ApiModelProperty(value = "记录创建时间", required = false)
    private Date createTime;
    /**
     * 记录修改时间
     */
    @ApiModelProperty(value = "记录修改时间", required = false)
    private Date modifyTime;
    /**
     * 是否删除 1是0否
     */
    @ApiModelProperty(value = "是否删除 1是0否", required = false)
    @TableLogic
    private Integer isDelete;
    /**
     * 一个周期中获得的订单数量
     */
    @ApiModelProperty(value = "一个周期中获得的订单数量", required = false)
    private Integer count;
}
